from typing import Optional

from blitzy_utils.logger import logger
from common_models.db_client import get_db_session
from common_models.models import (GithubInstallation, GithubInstallationStatus,
                                  VersionControlSystem,
                                  GitHubProjectRepo)
from sqlalchemy.orm import Session


def get_github_integration_by_installation_id(installation_id: str,
                                              session: Optional[Session] = None) -> GithubInstallation:
    """
    Fetches a GitHub integration instance by the given installation ID. This function queries
    the database to retrieve the `GithubInstallation` object associated with the provided
    installation ID. The function also ensures proper session management using the provided
    or an internally-created session, and optionally detaches the fetched object from the session.

    :param installation_id: The unique identifier of the GitHub installation to fetch.
    :type installation_id: str
    :param session: An optional SQLAlchemy session object. If not provided, a session will
        be generated internally for the database query.
    :type session: Optional[Session]
    :return: The GitHub installation instance associated with the given installation ID or `None`
        if no matching record is found.
    :rtype: GithubInstallation
    """
    with get_db_session(session) as session:
        github_installation = (session.query(GithubInstallation)
                               .filter(GithubInstallation.installation_id == installation_id)
                               .first())
        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation


def get_github_integration_by_github_user_id(github_user_id: str,
                                             session: Optional[Session] = None) -> GithubInstallation:
    """
    Fetches a GitHub installation associated with the provided GitHub user ID. The function retrieves
    the first pending GitHub installation request created by the user. The results are ordered by the
    `created_at` timestamp in descending order. If no session is provided, one is temporarily created
    for the query.

    If a GitHub installation is found and there was no session passed into the function, the installation
    object will be detached from the session before being returned to avoid unwanted persistence issues.

    :param github_user_id: The ID of the GitHub user for whom the integration is being retrieved.
    :type github_user_id: str
    :param session: An optional SQLAlchemy session object. If none is provided, a new session will
        be created within the function.
    :type session: Optional[Session]
    :return: An instance of `GithubInstallation` representing the associated GitHub installation, or
        `None` if no matching record is found.
    :rtype: GithubInstallation
    """
    with get_db_session(session) as session:
        github_installation = (
            session.query(GithubInstallation)
            .filter(GithubInstallation.requested_by == github_user_id,
                    GithubInstallation.status == GithubInstallationStatus.PENDING)
            .order_by(GithubInstallation.created_at.desc())
            .first()
        )

        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation


def get_github_installation_by_repo_id(repo_id, session: Optional[Session] = None) -> Optional[GithubInstallation]:
    """
    Fetch the active GitHub installation associated with a repository ID.

    This function retrieves the GitHub installation corresponding to the organization
    linked to the provided repository ID. If the repository does not exist or its
    organization ID is unavailable, or if no active installation is found, it returns
    None. The database session can either be provided or managed within the function.

    :param repo_id: The unique identifier of the repository.
    :type repo_id: int
    :param session: An optional SQLAlchemy session. If not provided, a new session is
        created and managed internally.
    :type session: Optional[Session]
    :return: The active GithubInstallation instance associated with the repository, or
        None if no such installation exists.
    :rtype: Optional[GithubInstallation]
    """
    with get_db_session(session) as session:
        project_repo = session.query(GitHubProjectRepo).filter(
            GitHubProjectRepo.repo_id == repo_id
        ).first()

        if not project_repo or not project_repo.org_id:
            logger.warning(f"No project repository found for repo_id {repo_id} or missing org_id")
            return None

        github_installation = session.query(GithubInstallation).filter(
            GithubInstallation.target_id == project_repo.org_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE
        ).order_by(GithubInstallation.created_at.desc()).first()

        return github_installation


def get_github_installation_by_target_name(target_name: str,
                                           session: Optional[Session] = None) -> Optional[GithubInstallation]:
    """
    Fetches an active GitHub installation by its target name.

    :param target_name: Name of the target to search for
    :type target_name: str
    :param session: Optional database session
    :type session: Optional[Session]
    :return: The corresponding GitHub installation if found, otherwise None
    :rtype: Optional[GithubInstallation]
    """
    with get_db_session(session) as session:
        github_installation = session.query(GithubInstallation).filter(
            GithubInstallation.target_name == target_name,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE
        ).first()

        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation


def get_github_installation_by_user(user_id: str, session: Optional[Session] = None, svc_type: VersionControlSystem = VersionControlSystem.GITHUB) -> Optional[GithubInstallation]:
    """
    Fetches an active GitHub installation by user_id.

    :param user_id: ID of the user to search for
    :type user_id: str
    :param session: Optional database session
    :type session: Optional[Session]
    :return: The corresponding GitHub installation if found, otherwise None
    :rtype: Optional[GithubInstallation]
    """
    with get_db_session(session) as session:
        # First search: by user_id only for active installations
        github_installation = session.query(GithubInstallation).filter(
            GithubInstallation.user_id == user_id,
            GithubInstallation.status == GithubInstallationStatus.ACTIVE,
            GithubInstallation.svc_type == svc_type
        ).first()
        
    
        if github_installation and not session:
            session.expunge(github_installation)
        return github_installation